import sys
import os
import codecs
import time
import json
import requests
from PyQt5.QtWidgets import (QA<PERSON>lication, QWidget, QVBoxLayout, QHBoxLayout,
                           QPushButton, QTextEdit, QLabel, QFrame,
                           QSizePolicy, QProgressBar, QMainWindow, QTabWidget,
                           QTextBrowser, QTreeWidget, QTreeWidgetItem, QDialog,
                           QMessageBox, QLineEdit, QFileDialog, QGridLayout, QComboBox,
                           QStackedWidget, QButtonGroup, QAbstractItemView)
from PyQt5.QtCore import Qt, QTimer, QSize, pyqtSignal, QEvent, QThread, QUrl, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QTextCursor, QIcon, QPixmap, QColor, QDesktopServices
from log_management import get_logger # Import the logger

# --- Dify API Configuration (from query.py) ---
DIFY_BASE_URL = "http://127.0.0.1/v1"
DIFY_API_KEY = "app-sm6XXFhw6kLdQyouKEt5sFNq" 
USER_ID = "dify_qt_chat_user" 

class DifyApiThread(QThread):
    """Handles Dify API chat requests in a separate thread."""
    response_received = pyqtSignal(str)
    message_end = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    thinking_started = pyqtSignal()
    
    def __init__(self, message, conversation_id=None, parent=None):
        super().__init__(parent)
        self.message = message
        self.conversation_id = conversation_id
        self.current_assistant_message_id = None
        
    def run(self):
        try:
            headers = {
                "Authorization": f"Bearer {DIFY_API_KEY}",
                "Content-Type": "application/json"
            }
            payload = {
                "query": self.message,
                "user": USER_ID,
                "response_mode": "streaming",
                "inputs": {},
                "auto_generate_name": True
            }
            if self.conversation_id:
                payload["conversation_id"] = self.conversation_id
            
            self.thinking_started.emit()
            
            response = requests.post(
                f"{DIFY_BASE_URL}/chat-messages",
                headers=headers,
                json=payload,
                stream=True,
                timeout=120
            )
            response.raise_for_status()
            
            for line in response.iter_lines():
                if line:
                    line_text = line.decode('utf-8')
                    if line_text.startswith('data: '):
                        json_str = line_text[len('data: '):]
                        try:
                            data = json.loads(json_str)
                            event_type = data.get('event')
                            
                            if 'conversation_id' in data and not self.conversation_id:
                                self.conversation_id = data['conversation_id']
                            
                            if event_type == 'agent_thought':
                                continue
                            
                            elif event_type in ['message', 'agent_message']:
                                answer_chunk = data.get('answer', '')
                                if self.current_assistant_message_id is None:
                                    self.current_assistant_message_id = data.get('message_id')
                                self.response_received.emit(answer_chunk)
                            
                            elif event_type == 'message_end':
                                metadata = data.get('metadata', {})
                                usage = metadata.get('usage', {})
                                end_data = {
                                    'conversation_id': data.get('conversation_id', self.conversation_id),
                                    'message_id': data.get('message_id', self.current_assistant_message_id),
                                    'usage': usage
                                }
                                self.message_end.emit(end_data)
                                break
                            
                            elif event_type == 'error':
                                error_msg = data.get('message', 'Unknown streaming error')
                                self.error_occurred.emit(f"API Error: {data.get('code', 'N/A')} - {error_msg}")
                                break
                                
                        except json.JSONDecodeError:
                            if "ping" not in line_text:
                                pass  # 静默处理解析错误
                        except Exception as e:
                            self.error_occurred.emit(f"Error processing stream data: {str(e)}")
                            break
            
        except requests.exceptions.RequestException as e:
            self.error_occurred.emit(f"Network error: {str(e)}")
        except Exception as e:
            self.error_occurred.emit(f"An unexpected error occurred: {str(e)}")

class AIChatWidget(QWidget):
    """AI Chat Interface, adapted to be a QWidget."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.conversation_id = None
        self.current_assistant_message = ""
        self.is_first_assistant_chunk = True
        self.in_thinking_block = False  # 标记是否在<think>块内
        self.thinking_buffer = ""  # 用于存储正在思考的内容
        self.deep_thinking_enabled = False  # 深度思考模式开关，默认关闭
        self.local_knowledge = {}  # 本地知识库
        self.faq_data = {}  # FAQ交互式问答数据
        self.current_faq_node = None  # 当前FAQ节点
        self.faq_conversation_history = []  # FAQ对话历史
        self.load_local_knowledge()  # 加载本地知识库
        self.load_faq_data()  # 加载FAQ数据
        self.init_ui()

    def load_local_knowledge(self):
        """加载知识库"""
        try:
            chat_json_path = os.path.join(os.path.dirname(__file__), 'chat.json')
            if os.path.exists(chat_json_path):
                with open(chat_json_path, 'r', encoding='utf-8') as f:
                    self.local_knowledge = json.load(f)
                get_logger().log_user_action("知识库加载成功")
            else:
                get_logger().log_user_action("知识库文件不存在")
                self.local_knowledge = {}
        except Exception as e:
            get_logger().log_user_action(f"加载知识库失败: {e}")
            self.local_knowledge = {}

    def load_faq_data(self):
        """加载FAQ交互式问答数据"""
        try:
            faq_json_path = os.path.join(os.path.dirname(__file__), 'faq.json')
            if os.path.exists(faq_json_path):
                with open(faq_json_path, 'r', encoding='utf-8') as f:
                    self.faq_data = json.load(f)
                get_logger().log_user_action("FAQ数据加载成功")
            else:
                get_logger().log_user_action("FAQ数据文件不存在")
                self.faq_data = {}
        except Exception as e:
            get_logger().log_user_action(f"加载FAQ数据失败: {e}")
            self.faq_data = {}

    def init_ui(self):
        self.setStyleSheet(f"""
            QWidget {{
                background-color: #1f9cf0;
            }}
            QTextEdit#ChatHistory {{
                border: 1px solid #dcdcdc;
                border-radius: 10px;
                background-color: white;
                font-size: 22px;
                font-family: Microsoft YaHei;
                padding: 15px;
                line-height: 1.5;
            }}
            QLineEdit#InputField {{
                border: 1px solid #dcdcdc;
                border-radius: 10px;
                padding: 15px;
                font-size: 18px;
                font-family: Microsoft YaHei;
                background-color: white;
            }}
            QPushButton {{
                background-color: #1C64F2;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
                font-size: 18px;
                font-weight: bold;
                font-family: Microsoft YaHei;
            }}
            QPushButton:hover {{
                background-color: #165CE3;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
            }}
            QLabel#TitleLabel {{
                font-size: 22px;
                font-weight: bold;
                font-family: Microsoft YaHei;
                color: #2c3e50;
                padding-bottom: 15px;
            }}
            QFrame#InputFrame {{
                background-color: #f8f9fa;
                border-top: 1px solid #e0e0e0;
                padding: 18px;
            }}
        """)

        main_layout = QVBoxLayout(self) 
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        self.chat_history_display = QTextEdit()
        self.chat_history_display.setObjectName("ChatHistory")
        self.chat_history_display.setReadOnly(True)
        main_layout.addWidget(self.chat_history_display, 1)

        # 添加滚动控制变量
        self.user_is_scrolling = False
        self.scroll_timer = QTimer()
        self.scroll_timer.setSingleShot(True)
        self.scroll_timer.timeout.connect(self.reset_scroll_flag)

        # 监听滚动条变化
        scrollbar = self.chat_history_display.verticalScrollBar()
        scrollbar.valueChanged.connect(self.on_scroll_changed)

        input_frame = QFrame()
        input_frame.setObjectName("InputFrame")
        input_layout = QVBoxLayout(input_frame)
        input_layout.setContentsMargins(0,0,0,0)
        input_layout.setSpacing(8)

        self.input_field = QLineEdit()
        self.input_field.setObjectName("InputField")
        self.input_field.setPlaceholderText("在此输入您的问题...")
        self.input_field.returnPressed.connect(self.send_message)
        self.input_field.setMinimumHeight(50)  # 增加输入框高度
        input_layout.addWidget(self.input_field)
        
        buttons_hbox = QHBoxLayout()

        # 添加深度思考按钮开关
        self.deep_thinking_button = QPushButton("深度思考: 关")
        self.deep_thinking_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 10px;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
                margin-right: 10px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
            QPushButton:pressed {
                background-color: #374151;
            }
        """)
        self.deep_thinking_button.clicked.connect(self.toggle_deep_thinking)
        buttons_hbox.addWidget(self.deep_thinking_button)

        self.send_button = QPushButton("发送")
        self.send_button.clicked.connect(self.send_message)
        buttons_hbox.addWidget(self.send_button)

        self.new_conversation_button = QPushButton("新对话")
        self.new_conversation_button.setStyleSheet(f"""
            QPushButton {{ background-color: #6c757d; }}
            QPushButton:hover {{ background-color: #5a6268; }}
        """)
        self.new_conversation_button.clicked.connect(self.start_new_conversation)
        buttons_hbox.addWidget(self.new_conversation_button)
        
        input_layout.addLayout(buttons_hbox)
        main_layout.addWidget(input_frame)

        self.append_styled_message("AI助手", "您好！我是故障诊断助手，有什么可以帮助您的吗？", QColor("#e9f5ff"), QColor("#1f78b4"))

    def toggle_deep_thinking(self):
        """切换深度思考模式"""
        self.deep_thinking_enabled = not self.deep_thinking_enabled

        if self.deep_thinking_enabled:
            self.deep_thinking_button.setText("深度思考: 开")
            self.deep_thinking_button.setStyleSheet("""
                QPushButton {
                    background-color: #3B82F6;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 15px 25px;
                    font-size: 14px;
                    font-weight: bold;
                    margin-right: 10px;
                }
                QPushButton:hover {
                    background-color: #2563EB;
                }
                QPushButton:pressed {
                    background-color: #1D4ED8;
                }
            """)
            self.input_field.setPlaceholderText("已切换到深度思考模式")
        else:
            self.deep_thinking_button.setText("深度思考: 关")
            self.deep_thinking_button.setStyleSheet("""
                QPushButton {
                    background-color: #6B7280;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 15px 25px;
                    font-size: 14px;
                    font-weight: bold;
                    margin-right: 10px;
                }
                QPushButton:hover {
                    background-color: #4B5563;
                }
                QPushButton:pressed {
                    background-color: #374151;
                }
            """)
            self.input_field.setPlaceholderText("已切换到普通模式")

        # 延迟恢复原始占位符文本
        QTimer.singleShot(2000, lambda: self.input_field.setPlaceholderText("在此输入您的问题..."))

        # 重置FAQ对话状态
        if not self.deep_thinking_enabled:
            self.reset_faq_conversation()

        get_logger().log_user_action(f"深度思考模式: {'开启' if self.deep_thinking_enabled else '关闭'}")

    def reset_faq_conversation(self):
        """重置FAQ对话状态"""
        self.current_faq_node = None
        self.faq_conversation_history = []

    def detect_scenario_from_input(self, user_input):
        """根据用户输入自动检测诊断场景"""
        if not self.faq_data or "scenarios" not in self.faq_data:
            return None

        user_input_lower = user_input.lower().strip()
        scenarios = self.faq_data["scenarios"]

        # 定义关键词映射，使用更精确的匹配
        scenario_keywords = {
            "上位机数据读取失败": [
                "上位机", "数据读取", "读取失败", "无数据", "数据获取",
                "can分析仪", "can", "毫米波雷达", "毫米波", "通信异常", "连接失败"
            ],
            "联合标定运行出错": [
                "联合标定", "标定出错", "标定失败", "标定异常", "运行出错",
                "相机标定", "相机", "init.yaml", "配置文件", "端口异常", "分辨率", "以太网ip"
            ],
            "你好": ["你好"]
        }

        # 计算每个场景的匹配分数
        best_scenario = None
        best_score = 0

        for scenario_name, keywords in scenario_keywords.items():
            if scenario_name in scenarios:
                score = 0
                for keyword in keywords:
                    if keyword in user_input_lower:
                        score += 1

                if score > best_score:
                    best_score = score
                    best_scenario = scenario_name

        # 如果匹配分数太低，返回None
        if best_score == 0:
            return None

        return best_scenario

    def start_faq_conversation_with_input(self, user_input):
        """根据用户输入开始FAQ交互式问答"""
        if not self.faq_data:
            return "抱歉，FAQ数据未加载成功。"

        # 重置对话状态
        self.reset_faq_conversation()

        # 检查是否为新的多场景格式
        if "scenarios" in self.faq_data:
            # 自动检测场景
            detected_scenario = self.detect_scenario_from_input(user_input)

            if detected_scenario:
                scenario_data = self.faq_data["scenarios"][detected_scenario]
                start_message = scenario_data.get("start", "开始故障诊断")
                flowchart_image = scenario_data.get("flowchart_image")
                self.current_faq_node = scenario_data.get("next")
                self.faq_conversation_history.append({"type": "start", "content": start_message})

                # 如果有流程图，先显示图片
                if flowchart_image and os.path.exists(flowchart_image):
                    self.append_image_message("AI助手", flowchart_image,
                                            f"故障诊断流程图\n\n{start_message}\n\n" + self.get_current_question())
                    return None  # 返回None表示已经直接显示了消息
                else:
                    return f"🔧 故障诊断开始\n\n{start_message}\n\n" + self.get_current_question()
            else:
                # 没有匹配的场景
                return f"抱歉，知识库中没有该类型的故障信息，请切换至深度思考模式。"
        else:
            # 兼容旧格式
            start_message = self.faq_data.get("start", "开始故障诊断")
            self.current_faq_node = self.faq_data.get("next")
            self.faq_conversation_history.append({"type": "start", "content": start_message})
            return f"🔧 故障诊断开始\n\n问题描述：{start_message}\n\n" + self.get_current_question()

    def get_current_question(self):
        """获取当前问题"""
        if not self.current_faq_node:
            return "回复完成。"

        if "condition" in self.current_faq_node:
            return f"请回答：{self.current_faq_node['condition']}\n\n请回复「是」或「否」"
        elif "action" in self.current_faq_node:
            action = self.current_faq_node["action"]
            self.faq_conversation_history.append({"type": "action", "content": action})

            # 检查是否有下一步
            if "next" in self.current_faq_node:
                self.current_faq_node = self.current_faq_node["next"]
                return f"✅ 操作步骤：{action}\n\n" + self.get_current_question()
            else:
                self.current_faq_node = None
                return f"故障诊断完成！"

        return "诊断流程异常。"

    def process_faq_response(self, user_input):
        """处理FAQ用户回复"""
        if not self.current_faq_node:
            return "当前没有进行中的故障诊断。请输入任意内容开始新的诊断。"

        user_input_lower = user_input.lower().strip()

        # 处理是/否回答
        if "condition" in self.current_faq_node:
            if user_input_lower in ["是", "yes", "y", "1", "对", "正确", "有", "存在", "有故障码", "有故障", "有错误", "有异常", "有报警", "有提示", "有信息","有故障"]:
                self.faq_conversation_history.append({"type": "answer", "content": "是", "question": self.current_faq_node["condition"]})
                next_node = self.current_faq_node.get("yes")
                # 如果下一个节点是action，需要特殊处理
                if next_node and "action" in next_node:
                    action = next_node["action"]
                    self.faq_conversation_history.append({"type": "action", "content": action})
                    if "next" in next_node:
                        self.current_faq_node = next_node["next"]
                        return f"✅ 操作步骤：{action}\n\n" + self.get_current_question()
                    else:
                        self.current_faq_node = None
                        return f"故障诊断完成！"
                else:
                    self.current_faq_node = next_node
                    return self.get_current_question()
            elif user_input_lower in ["否", "no", "n", "0", "不", "错误", "没有", "无","不存在", "无故障码", "无故障", "无错误", "无异常", "无报警", "无提示", "无信息","无故障", "不是", "不对", "不正确"]:
                self.faq_conversation_history.append({"type": "answer", "content": "否", "question": self.current_faq_node["condition"]})
                next_node = self.current_faq_node.get("no")
                # 如果下一个节点是action，需要特殊处理
                if next_node and "action" in next_node:
                    action = next_node["action"]
                    self.faq_conversation_history.append({"type": "action", "content": action})
                    if "next" in next_node:
                        self.current_faq_node = next_node["next"]
                        return f"✅ 操作步骤：{action}\n\n" + self.get_current_question()
                    else:
                        self.current_faq_node = None
                        return f"故障诊断完成！"
                else:
                    self.current_faq_node = next_node
                    return self.get_current_question()
            else:
                return f"请明确回答「是」或「否」\n\n当前问题：{self.current_faq_node['condition']}"

        return "请按照提示进行操作。"

    def find_local_match(self, query):
        """在本地知识库中查找匹配的内容"""
        query_lower = query.lower().strip()

        # 直接匹配键名
        for key, value in self.local_knowledge.items():
            if key.lower() in query_lower or query_lower in key.lower():
                if isinstance(value, list) and len(value) > 0:
                    return value[0]  # 返回第一个匹配项
                elif isinstance(value, str):
                    return value

        # 关键词匹配
        keywords_map = {
            "毫米波": ["77GHZ毫米波雷达调试流程", "毫米波雷达上位机无数据"],
            "激光雷达": ["32线激光雷达调试流程", "激光雷达上位机无数据"],
            "标定": ["联合标定调试流程"],
            "调试": ["77GHZ毫米波雷达调试流程", "32线激光雷达调试流程", "联合标定调试流程"],
            "无数据": ["毫米波雷达上位机无数据", "激光雷达上位机无数据"],
            "故障": ["毫米波雷达上位机无数据", "激光雷达上位机无数据"],
            "你好": ["你好"],
            "hello": ["你好"],
            "hi": ["你好"]
        }

        for keyword, possible_keys in keywords_map.items():
            if keyword in query_lower:
                for possible_key in possible_keys:
                    if possible_key in self.local_knowledge:
                        value = self.local_knowledge[possible_key]
                        if isinstance(value, list) and len(value) > 0:
                            return value[0]
                        elif isinstance(value, str):
                            return value

        return None

    def handle_local_query(self, query):
        """处理查询 - 现在使用FAQ交互式问答"""
        # 如果当前没有进行中的FAQ对话，开始新的对话
        if self.current_faq_node is None and not self.faq_conversation_history:
            response = self.start_faq_conversation_with_input(query)
        else:
            # 处理用户在FAQ对话中的回复
            response = self.process_faq_response(query)

        # 开始模拟检索过程
        self.simulate_fault_retrieval(query, response)

    def simulate_fault_retrieval(self, query, response):
        """模拟故障库检索过程"""
        # 初始化状态更新计数器
        self.retrieval_step = 0
        self.query_response = response
        self.query_text = query

        # 创建状态更新定时器
        self.status_update_timer = QTimer()
        self.status_update_timer.timeout.connect(self.update_fault_retrieval_status)
        self.status_update_timer.start(800)  # 每800毫秒更新一次状态

        # 创建最终结果显示定时器
        self.final_result_timer = QTimer()
        self.final_result_timer.setSingleShot(True)
        self.final_result_timer.timeout.connect(self.show_final_local_response)
        self.final_result_timer.start(3000)  # 3秒后显示最终结果

    def update_fault_retrieval_status(self):
        """更新检索状态显示"""
        status_messages = [
            "正在分析问题...",
            "正在生成诊断流程...",
            "正在准备交互问答...",
            "正在整理诊断结果..."
        ]

        if self.retrieval_step < len(status_messages):
            self.input_field.setPlaceholderText(status_messages[self.retrieval_step])
            self.retrieval_step += 1
        else:
            # 停止状态更新
            self.status_update_timer.stop()

    def show_final_local_response(self):
        """显示最终的本地查询响应"""
        # 停止所有定时器
        if hasattr(self, 'status_update_timer'):
            self.status_update_timer.stop()
        if hasattr(self, 'final_result_timer'):
            self.final_result_timer.stop()

        if self.query_response is not None:
            self.input_field.setPlaceholderText("诊断流程生成完成")
            # 模拟打字效果显示结果
            self.simulate_typing_effect(self.query_response)
        else:
            # 如果返回None，说明已经直接显示了图片消息，只需要恢复界面状态
            self.input_field.setPlaceholderText("诊断流程生成完成")

        # 重新启用输入控件
        self.send_button.setEnabled(True)
        self.new_conversation_button.setEnabled(True)

        # 延迟恢复原始占位符文本
        QTimer.singleShot(2000, lambda: self.input_field.setPlaceholderText("在此输入您的问题..."))

    def simulate_typing_effect(self, text):
        """模拟打字效果显示文本"""
        # 重置消息状态
        self.current_assistant_message = ""
        self.is_first_assistant_chunk = True

        # 分批显示文本，模拟流式效果
        self.typing_text = text
        self.typing_index = 0
        self.typing_timer = QTimer()
        self.typing_timer.timeout.connect(self.show_next_char)
        self.typing_timer.start(30)  # 每30毫秒显示一个字符

    def show_next_char(self):
        """显示下一个字符"""
        if self.typing_index < len(self.typing_text):
            char = self.typing_text[self.typing_index]
            # 使用现有的响应处理方法
            self.on_response_received(char)
            self.typing_index += 1
        else:
            # 打字完成
            self.typing_timer.stop()
            # 模拟消息结束
            self.on_message_end({"conversation_id": None})

    def append_styled_message(self, sender, message, bg_color, text_color, is_user=False):
        cursor = self.chat_history_display.textCursor()
        cursor.movePosition(QTextCursor.End)

        formatted_message = message.replace("\n", "<br>")
        cursor.insertHtml(f"<div style='margin: 10px 0; padding: 8px; background-color: {bg_color.name()}; "
                          f"color: {text_color.name()}; border-radius: 8px; word-wrap: break-word; display: block;'>"
                          f"<strong>{sender}：</strong> {formatted_message}</div><br>")
        if not self.user_is_scrolling:
            self.chat_history_display.ensureCursorVisible()

    def append_image_message(self, sender, image_path, message="", bg_color=None, text_color=None):
        """添加包含图片的消息"""
        if bg_color is None:
            bg_color = QColor("#e9f5ff")
        if text_color is None:
            text_color = QColor("#1f78b4")

        cursor = self.chat_history_display.textCursor()
        cursor.movePosition(QTextCursor.End)

        # 检查图片文件是否存在
        if not os.path.exists(image_path):
            # 如果图片不存在，只显示文本消息
            if message:
                self.append_styled_message(sender, message, bg_color, text_color)
            return

        # 获取聊天区域的宽度来计算图片显示大小
        chat_width = self.chat_history_display.width()
        max_image_width = int(chat_width * 0.6)  # 图片最大宽度为聊天区域的60%

        cursor.insertHtml(f"<div style='margin: 5px 0; text-align: left; clear: both;'>")
        cursor.insertHtml(f"<div style='display: inline-block; max-width: 75%; padding: 10px 15px; border-radius: 15px; "
                          f"background-color: {bg_color.name()}; color: {text_color.name()}; "
                          f"text-align: left; word-wrap: break-word;'>")
        cursor.insertHtml(f"<strong style='color: {text_color.darker(120).name()};'>{sender}:</strong><br>")

        # 插入图片，设置最大宽度以适应聊天区域
        cursor.insertHtml(f"<img src='file:///{os.path.abspath(image_path)}' "
                          f"style='max-width: {max_image_width}px; height: auto; border-radius: 8px; margin: 10px 0;'><br>")

        # 如果有文本消息，也显示出来
        if message:
            formatted_message = message.replace("\n", "<br>")
            cursor.insertHtml(formatted_message)

        cursor.insertHtml("</div></div><br><br>")
        if not self.user_is_scrolling:
            self.chat_history_display.ensureCursorVisible()
    
    def send_message(self):
        user_message = self.input_field.text().strip()
        if not user_message:
            return

        # Log the user's message
        get_logger().log_user_action(f"FAQ AI诊断: '{user_message}'")

        self.append_styled_message("用户", user_message, QColor("#e0f7fa"), QColor("#00796b"), is_user=True)
        self.input_field.clear()

        self.send_button.setEnabled(False)
        self.new_conversation_button.setEnabled(False)

        self.current_assistant_message = ""
        self.is_first_assistant_chunk = True

        # 根据深度思考模式选择处理方式
        if self.deep_thinking_enabled:
            # 深度思考模式：使用 Dify API
            self.input_field.setPlaceholderText("AI思考中...")

            self.api_thread = DifyApiThread(user_message, self.conversation_id, self)
            self.api_thread.response_received.connect(self.on_response_received)
            self.api_thread.message_end.connect(self.on_message_end)
            self.api_thread.error_occurred.connect(self.on_error_occurred)
            self.api_thread.thinking_started.connect(lambda: self.input_field.setPlaceholderText("AI思考中..."))
            self.api_thread.start()
        else:
            # 本地匹配模式：使用本地 JSON 数据
            self.handle_local_query(user_message)

    def on_response_received(self, chunk):
        # 检查思考标签
        if "<think>" in chunk:
            # 标记为进入思考区块
            self.in_thinking_block = True
            # 提取<think>前面的内容
            before_think = chunk.split("<think>")[0]
            if before_think:
                self.process_regular_chunk(before_think)
            self.thinking_buffer = ""  # 清空思考缓存
            # 添加<think>后面的内容到思考缓存
            if len(chunk.split("<think>")) > 1:
                self.thinking_buffer += chunk.split("<think>")[1]
            self.input_field.setPlaceholderText("AI正在思考中...")
            return
        
        if self.in_thinking_block:
            if "</think>" in chunk:
                # 思考结束，获取</think>后的内容
                self.in_thinking_block = False
                after_think = chunk.split("</think>")[1] if len(chunk.split("</think>")) > 1 else ""
                self.thinking_buffer = ""  # 清空思考缓存
                if after_think:
                    self.process_regular_chunk(after_think)
                self.input_field.setPlaceholderText("AI回复中...")
                return
            else:
                # 仍在思考中，添加到思考缓存
                self.thinking_buffer += chunk
                return
                
        # 处理正常内容
        self.process_regular_chunk(chunk)
    
    def process_regular_chunk(self, chunk):
        """处理常规响应块，不含思考内容"""
        if self.is_first_assistant_chunk:
            self.input_field.setPlaceholderText("AI回复中...")
            # 直接添加AI助手消息的开始部分
            cursor = self.chat_history_display.textCursor()
            cursor.movePosition(QTextCursor.End)
            cursor.insertHtml(f"<div style='margin: 10px 0; padding: 8px; background-color: {QColor('#e9f5ff').name()}; "
                             f"color: {QColor('#1f78b4').name()}; border-radius: 8px; word-wrap: break-word; display: block;'>"
                             f"<strong>AI助手：</strong> ")
            self.is_first_assistant_chunk = False

        self.current_assistant_message += chunk

        # 简单地在当前位置插入新的文本块
        cursor = self.chat_history_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        # 插入文本，但不包含HTML标签
        cursor.insertText(chunk)

        if not self.user_is_scrolling:
            self.chat_history_display.ensureCursorVisible()
    
    def on_message_end(self, data):
        # 结束当前AI助手消息的div标签
        if self.current_assistant_message:
            cursor = self.chat_history_display.textCursor()
            cursor.movePosition(QTextCursor.End)
            cursor.insertHtml("</div><br>")

        self.conversation_id = data.get('conversation_id')
        self.input_field.setPlaceholderText("在此输入您的问题...")
        self.send_button.setEnabled(True)
        self.new_conversation_button.setEnabled(True)
        self.current_assistant_message = ""
        self.is_first_assistant_chunk = True
    
    def on_error_occurred(self, error_message):
        # 如果有未完成的AI助手消息，先结束它
        if self.current_assistant_message and not self.is_first_assistant_chunk:
            cursor = self.chat_history_display.textCursor()
            cursor.movePosition(QTextCursor.End)
            cursor.insertHtml("</div><br>")

        self.append_styled_message("系统", f"错误: {error_message}", QColor("#ffebee"), QColor("#c62828"))
        self.input_field.setPlaceholderText("在此输入您的问题...")
        self.send_button.setEnabled(True)
        self.new_conversation_button.setEnabled(True)
        self.current_assistant_message = ""
        self.is_first_assistant_chunk = True

    def on_scroll_changed(self, value):
        """滚动条变化时的处理"""
        scrollbar = self.chat_history_display.verticalScrollBar()
        # 如果用户手动滚动到非底部位置，标记为用户正在滚动
        if value < scrollbar.maximum() - 10:  # 给一点容差
            self.user_is_scrolling = True
            self.scroll_timer.start(2000)  # 2秒后重置标志
        else:
            self.user_is_scrolling = False

    def reset_scroll_flag(self):
        """重置滚动标志"""
        self.user_is_scrolling = False

    def start_new_conversation(self):
        self.conversation_id = None
        self.chat_history_display.clear()
        # 重置FAQ对话状态
        self.reset_faq_conversation()
        if self.deep_thinking_enabled:
            self.append_styled_message("AI助手", "您好！我是AI智能诊断助手，有什么可以帮助您的吗？", QColor("#e9f5ff"), QColor("#1f78b4"))
        else:
            self.append_styled_message("AI助手", "您好！我是故障诊断助手，有什么可以帮助您的吗？", QColor("#e9f5ff"), QColor("#1f78b4"))
        self.input_field.setPlaceholderText("在此输入您的问题...")
        self.input_field.setFocus()

class FeedbackDialog(QDialog):
    """反馈对话框，用于提交用户反馈"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("提交反馈")
        self.resize(500, 300)
        self.setStyleSheet(f"""
            QDialog {{
                background-color: white;
            }}
        """)
        
        layout = QVBoxLayout(self)
        
        # 说明文本
        description = QLabel("请描述您遇到的问题或建议，我们将尽快处理您的反馈。")
        description.setWordWrap(True)
        description.setStyleSheet(f"""
            font-size: 16px;
            font-family: Microsoft YaHei;
            color: #555;
            margin-bottom: 10px;
        """)
        layout.addWidget(description)
        
        # 反馈内容输入框
        self.feedback_input = QTextEdit()
        self.feedback_input.setPlaceholderText("请在此输入您的反馈...")
        self.feedback_input.setStyleSheet(f"""
            QTextEdit {{
                border: 1px solid #dcdcdc;
                border-radius: 4px;
                padding: 8px;
                font-size: 14px;
                font-family: Microsoft YaHei;
                background-color: white;
            }}
        """)
        layout.addWidget(self.feedback_input)
        
        # 按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #f5f5f5;
                color: #333;
                border: 1px solid #dcdcdc;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 14px;
                font-family: Microsoft YaHei;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: #e0e0e0;
            }}
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        
        # 提交按钮
        submit_btn = QPushButton("提交")
        submit_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #1C64F2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 14px;
                font-family: Microsoft YaHei;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: #165CE3;
            }}
        """)
        submit_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(submit_btn)
        
        layout.addLayout(buttons_layout)

    def get_feedback_text(self):
        """获取反馈内容"""
        return self.feedback_input.toPlainText().strip()

class FileContentWindow(QMainWindow):
    """用于在新窗口中显示文件内容的现代化弹窗"""
    def __init__(self, file_path, parent=None):
        super().__init__(parent)
        self.file_path = file_path
        self.drag_position = None
        self.init_ui()

    def init_ui(self):
        # 设置窗口无标题栏
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.resize(1000, 750)  # 调整为更合适的宽高比

        # 创建主容器，使用更灰的主题
        main_container = QWidget()
        main_container.setStyleSheet("""
            QWidget {
                background-color: #e0e0e0;
                border-radius: 16px;
                border: 2px solid #c0c0c0;
            }
        """)
        self.setCentralWidget(main_container)

        # 主布局
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建标题栏
        self.create_title_bar(main_layout)

        # 创建内容区域
        self.create_content_area(main_layout)

        # 加载文件内容
        self.load_file_content()

        # 窗口居中显示
        self.center_window()

        # 添加淡入动画效果
        self.setWindowOpacity(0.0)
        self.fade_in_animation()

    def create_title_bar(self, parent_layout):
        """创建优雅的亮色标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(55)
        title_bar.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e8e8e8, stop:1 #d8d8d8);
                border-radius: 16px 16px 0px 0px;
                border-bottom: 1px solid #c0c0c0;
            }
        """)

        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(20, 0, 15, 0)

        # 文件图标 - 隐藏问号图标
        # icon_label = QLabel("�")
        # icon_label.setStyleSheet("""
        #     QLabel {
        #         font-size: 20px;
        #         color: #2c2c2c;
        #         background: transparent;
        #         padding: 0px;
        #         margin: 0px;
        #     }
        # """)
        # title_layout.addWidget(icon_label)

        # 标题文本 - 固定为"故障解决方案"
        self.title_label = QLabel("故障解决方案")
        self.title_label.setStyleSheet(f"""
            QLabel {{
                color: #2c2c2c;
                font-size: 22px;
                font-weight: bold;
                font-family: Microsoft YaHei, SimSun;
                background: transparent;
                padding-left: 18px;
            }}
        """)
        title_layout.addWidget(self.title_label)

        title_layout.addStretch()

        # 最小化按钮
        minimize_btn = QPushButton("━")
        minimize_btn.setFixedSize(35, 28)
        minimize_btn.setStyleSheet("""
            QPushButton {
                background-color: #b8b8b8;
                color: #2c2c2c;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                padding: 0px;
                margin: 0px;
            }
            QPushButton:hover {
                background-color: #a8a8a8;
                color: #1c1c1c;
            }
            QPushButton:pressed {
                background-color: #989898;
            }
        """)
        minimize_btn.clicked.connect(self.showMinimized)
        title_layout.addWidget(minimize_btn)

        # 关闭按钮
        close_btn = QPushButton("✕")
        close_btn.setFixedSize(35, 28)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff6b6b;
                color: #1c1c1c;
                border: none;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                padding: 0px;
                margin: 0px;
            }
            QPushButton:hover {
                background-color: #ff5252;
            }
            QPushButton:pressed {
                background-color: #ff4444;
            }
        """)
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)

        parent_layout.addWidget(title_bar)

        # 使标题栏可拖动
        title_bar.mousePressEvent = self.title_bar_mouse_press_event
        title_bar.mouseMoveEvent = self.title_bar_mouse_move_event

    def create_content_area(self, parent_layout):
        """创建内容显示区域"""
        content_container = QWidget()
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(25, 20, 25, 25)

        # 创建滚动区域
        scroll_area = QTextBrowser()
        scroll_area.setStyleSheet(f"""
            QTextBrowser {{
                background-color: #ffffff;
                border: none;
                border-radius: 16px;
                padding: 25px;
                font-size: 16px;
                font-family: Microsoft YaHei, SimSun;
                color: #2c2c2c;
                line-height: 1.6;
                selection-background-color: #b3d9ff;
                selection-color: #1c1c1c;
            }}
            QScrollBar:vertical {{
                background-color: #f0f0f0;
                width: 10px;
                border-radius: 5px;
                margin: 0px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #a0a0a0;
                border-radius: 5px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: #808080;
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
                height: 0px;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
            }}
        """)

        self.content_display = scroll_area
        content_layout.addWidget(scroll_area)

        # 添加底部操作栏
        self.create_action_bar(content_layout)

        parent_layout.addWidget(content_container)

    def create_action_bar(self, parent_layout):
        """创建底部操作栏"""
        action_bar = QWidget()
        action_bar.setFixedHeight(55)
        action_bar.setStyleSheet("""
            QWidget {
                background-color: #d0d0d0;
                border-top: 1px solid #b0b0b0;
                border-radius: 0px 0px 14px 14px;
            }
        """)

        action_layout = QHBoxLayout(action_bar)
        action_layout.setContentsMargins(20, 10, 20, 10)

        # 添加弹性空间，将复制按钮推到右侧
        action_layout.addStretch()

        # 复制内容按钮 - 移动到右侧（原系统打开按钮的位置）
        copy_btn = QPushButton("📋 复制内容")
        copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #b3d9ff;
                color: #1c1c1c;
                border: none;
                border-radius: 6px;
                padding: 0px;
                margin: 0px;
                font-size: 13px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #99ccff;
            }
            QPushButton:pressed {
                background-color: #80bfff;
            }
        """)
        copy_btn.clicked.connect(self.copy_content)
        action_layout.addWidget(copy_btn)

        parent_layout.addWidget(action_bar)

    def load_file_content(self):
        """加载并显示文件内容"""
        try:
            with codecs.open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 获取文件名（去掉.txt后缀）
            file_name = os.path.basename(self.file_path)
            if file_name.lower().endswith('.txt'):
                file_name = file_name[:-4]

            # 创建更灰的主题HTML内容
            html_content = f"""
            <div style="max-width: 100%; margin: 0 auto; background-color: #ffffff;">
                <div style="text-align: center; margin-bottom: 25px; padding: 18px;
                           background: linear-gradient(135deg, #b3d9ff 0%, #99ccff 100%);
                           border-radius: 10px; color: #1c1c1c; border: 1px solid #99ccff;">
                    <h1 style="margin: 0; font-size: 22px; font-weight: bold;
                               text-shadow: 0 1px 3px rgba(255,255,255,0.4);">
                        📋 {file_name}
                    </h1>
                </div>

                <div style="background-color: #ffffff; padding: 20px; border-radius: 8px;
                           border: 1px solid #c0c0c0; line-height: 1.7;">
            """

            # 处理内容，添加段落样式
            paragraphs = content.split('\n\n')  # 按双换行分段
            for paragraph in paragraphs:
                if paragraph.strip():
                    # 处理单行内的换行
                    lines = paragraph.strip().split('\n')
                    formatted_paragraph = '<br>'.join(lines)
                    html_content += f"""
                    <p style="margin-bottom: 9px; text-align: justify;
                             text-indent: 2em; color: #2c2c2c; font-size: 26px;">
                        {formatted_paragraph}
                    </p>
                    """
                else:
                    html_content += "<div style='height: 12px;'></div>"

            html_content += """
                </div>
            </div>
            """

            self.content_display.setHtml(html_content)

        except Exception as e:
            error_html = f"""
            <div style="text-align: center; padding: 50px; color: #d32f2f; background-color: #d8d8d8;">
                <h2 style="color: #d32f2f;">⚠️ 加载失败</h2>
                <p style="color: #2c2c2c;">无法加载文件内容: {str(e)}</p>
            </div>
            """
            self.content_display.setHtml(error_html)

    def copy_content(self):
        """复制内容到剪贴板"""
        try:
            with codecs.open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            clipboard = QApplication.clipboard()
            clipboard.setText(content)

            # 显示复制成功提示
            self.show_toast("内容已复制到剪贴板")
        except Exception as e:
            self.show_toast(f"复制失败: {str(e)}")

    def open_in_system(self):
        """在系统默认程序中打开文件"""
        try:
            QDesktopServices.openUrl(QUrl.fromLocalFile(self.file_path))
        except Exception as e:
            self.show_toast(f"打开失败: {str(e)}")

    def show_toast(self, message):
        """显示提示消息"""
        # 这里可以实现一个简单的提示框，暂时使用状态栏显示
        # 在实际应用中可以创建一个更美观的toast组件
        print(f"Toast: {message}")  # 临时使用print输出消息

    def fade_in_animation(self):
        """淡入动画效果"""
        self.animation = QPropertyAnimation(self, b"windowOpacity")
        self.animation.setDuration(300)
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        self.animation.start()

    def center_window(self):
        """使窗口在屏幕中央显示"""
        screen_geometry = QApplication.desktop().screenGeometry()
        window_geometry = self.frameGeometry()
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        self.move(window_geometry.topLeft())

    def title_bar_mouse_press_event(self, event):
        """标题栏鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def title_bar_mouse_move_event(self, event):
        """标题栏鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def closeEvent(self, event):
        """关闭事件，添加淡出动画"""
        self.close_animation = QPropertyAnimation(self, b"windowOpacity")
        self.close_animation.setDuration(200)
        self.close_animation.setStartValue(1.0)
        self.close_animation.setEndValue(0.0)
        self.close_animation.setEasingCurve(QEasingCurve.InCubic)
        self.close_animation.finished.connect(lambda: super(FileContentWindow, self).closeEvent(event))
        self.close_animation.start()

        # 暂时忽略关闭事件，等动画完成
        event.ignore()

class CommonFaultQueryWidget(QWidget):
    """常见故障查询界面，显示本地故障文档"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_dir = "../data"  # 数据目录
        self.feedback_dir = "../feedback" # 反馈目录
        self.init_ui()
        self.load_fault_categories()
        
    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)  # 进一步增大内边距
        main_layout.setSpacing(25)  # 进一步增大间距

        # 搜索框
        search_container = QFrame()
        search_container.setStyleSheet(f"background-color: #8cc4ff; border-radius: 12px; padding: 10px;")  # 增大圆角和内边距
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(20, 10, 20, 10)  # 增大内边距
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("请输入关键词搜索故障解决方案")
        self.search_input.setStyleSheet(f"""
            border: 1px solid #dcdcdc;
            padding: 15px;
            font-size: 18px;
            font-family: Microsoft YaHei;
        """)  # 增大内边距和字体
        self.search_input.setMinimumHeight(55)  # 增大高度
        self.search_input.returnPressed.connect(self.search_faults)
        search_layout.addWidget(self.search_input)
        
        search_btn = QPushButton("搜索")
        search_btn.setIcon(QIcon.fromTheme("edit-find"))
        search_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #1C64F2;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 15px 25px;
                font-size: 18px;
                font-family: Microsoft YaHei;
            }}
            QPushButton:hover {{ background-color: #165CE3; }}
        """)
        search_btn.setMinimumHeight(55)  # 增大高度
        search_btn.setMinimumWidth(120)  # 增大宽度
        search_btn.clicked.connect(self.search_faults)
        search_layout.addWidget(search_btn)
        main_layout.addWidget(search_container)
        
        # 文件树
        tree_label = QLabel("故障类型")
        tree_label.setAlignment(Qt.AlignCenter)
        tree_label.setStyleSheet(f"""
            font-size: 20px;
            font-family: Microsoft YaHei;
            color: #555;
            margin-top: 25px;
            margin-bottom: 15px;
        """)  # 增大字体和边距
        main_layout.addWidget(tree_label)
        
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderHidden(True)

        # 设置多选模式 - 支持扩展选择（Ctrl+点击多选，Shift+点击范围选择）
        self.file_tree.setSelectionMode(QAbstractItemView.ExtendedSelection)

        self.file_tree.setStyleSheet(f"""
            QTreeWidget {{
                border: 1px solid #dcdcdc;
                border-radius: 12px;  /* 增大圆角 */
                background-color: #8cc4ff;
                padding: 20px;  /* 增大内边距 */
                font-size: 18px;  /* 增大字体 */
                font-family: Microsoft YaHei;
                outline: none;  /* 移除焦点虚线框 */
            }}
            QTreeWidget::item {{
                padding: 10px;  /* 增大内边距 */
                border-radius: 6px;  /* 增大圆角 */
                min-height: 40px;  /* 设置最小高度 */
                outline: none;  /* 移除焦点虚线框 */
            }}
            QTreeWidget::item:hover {{
                background-color: #f0f7ff;
            }}
            QTreeWidget::item:selected {{
                background-color: #e3f2fd;
                color: #1C64F2;
                outline: none;  /* 移除焦点虚线框 */
            }}
            QTreeWidget::item:focus {{
                outline: none;  /* 移除焦点虚线框 */
                border: none;  /* 移除焦点边框 */
            }}
        """)
        self.file_tree.itemDoubleClicked.connect(self.on_item_double_clicked)
        main_layout.addWidget(self.file_tree)
        
        # 左下角反馈按钮
        feedback_btn_layout = QHBoxLayout()
        feedback_btn_layout.addStretch()
        self.feedback_btn = QPushButton("反馈")
        self.feedback_btn.setIcon(QIcon.fromTheme("mail-message-new"))
        self.feedback_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #34495e;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 15px 30px;
                font-size: 18px;  /* 增大内边距和字体 */
                font-family: Microsoft YaHei;
            }}
            QPushButton:hover {{ background-color: #2c3e50; }}
        """)
        self.feedback_btn.setMinimumHeight(55)  # 增大高度
        self.feedback_btn.setMinimumWidth(150)  # 增大宽度
        self.feedback_btn.clicked.connect(self.show_feedback_dialog)
        feedback_btn_layout.addWidget(self.feedback_btn)
        main_layout.addLayout(feedback_btn_layout)
    
    def load_fault_categories(self):
        """加载故障类型到树形结构"""
        self.file_tree.clear()
        
        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            try:
                os.makedirs(self.data_dir)
                QMessageBox.information(self, "提示", f"数据目录 {self.data_dir} 不存在，已创建。")
            except Exception:
                # 静默处理异常
                pass
            return
        
        # 获取目录列表
        try:
            categories = os.listdir(self.data_dir)
        except Exception:
            # 静默处理异常
            return
            
        # 遍历数据目录
        for category in categories:
            category_path = os.path.join(self.data_dir, category)
            if os.path.isdir(category_path):
                try:
                    # 创建分类节点
                    category_item = QTreeWidgetItem(self.file_tree)
                    category_item.setText(0, category)
                    category_item.setData(0, Qt.UserRole, category_path)
            
                    # 添加文件到分类节点
                    for file_name in os.listdir(category_path):
                        if file_name.endswith(('.txt', '.png')):  # 添加对PNG文件的支持
                            # 去除文件后缀显示
                            display_name = os.path.splitext(file_name)[0]
                            file_item = QTreeWidgetItem(category_item)
                            file_item.setText(0, display_name)
                            file_item.setData(0, Qt.UserRole, os.path.join(category_path, file_name))
                except Exception:
                    # 静默处理异常
                    continue


    def on_item_double_clicked(self, item, column=None):
        """处理树形项目的双击事件"""
        # 仅处理子项（具体问题）
        if item.childCount() == 0:
            file_path = item.data(0, Qt.UserRole)
            if file_path and os.path.exists(file_path):
                # Log the action
                get_logger().log_user_action(f"查看FAQ: '{item.text(0)}'")
                
                # 根据文件类型选择打开方式
                if file_path.lower().endswith('.png'):
                    # 对于PNG文件，使用系统默认查看器打开
                    QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
                else:
                    # 对于其他文件（此处应为.txt），使用自定义窗口显示
                    self.file_content_window = FileContentWindow(file_path, self)
                    self.file_content_window.show()
            else:
                QMessageBox.warning(self, "错误", f"文件不存在或路径无效：\n{file_path}")
    
    def search_faults(self):
        """搜索故障分类（文件夹）"""
        search_text = self.search_input.text().strip().lower()
        if not search_text:
            self.load_fault_categories()
            return

        self.file_tree.clear()
        found_categories = []

        # 遍历所有分类目录，只搜索文件夹名
        for category in os.listdir(self.data_dir):
            category_path = os.path.join(self.data_dir, category)
            if os.path.isdir(category_path):
                # 检查分类名（文件夹名）是否包含搜索文本
                if search_text in category.lower():
                    found_categories.append(category)

                    try:
                        # 创建分类节点
                        category_item = QTreeWidgetItem(self.file_tree)
                        category_item.setText(0, category)
                        category_item.setData(0, Qt.UserRole, category_path)

                        # 添加该分类下的所有文件
                        for file_name in os.listdir(category_path):
                            if file_name.endswith(('.txt', '.png')):
                                # 去除文件后缀显示
                                display_name = os.path.splitext(file_name)[0]
                                file_item = QTreeWidgetItem(category_item)
                                file_item.setText(0, display_name)
                                file_item.setData(0, Qt.UserRole, os.path.join(category_path, file_name))
                    except Exception:
                        # 静默处理异常
                        continue

        if self.file_tree.topLevelItemCount() == 0:
            QMessageBox.information(self, "搜索结果", f"未找到包含 '{search_text}' 的分类")

    def show_feedback_dialog(self):
        dialog = FeedbackDialog(self)
        if dialog.exec_():
            feedback_text = dialog.get_feedback_text()
            if feedback_text:
                self.save_feedback(feedback_text)
        else:
                QMessageBox.warning(self, "提示", "反馈内容不能为空！")

    def save_feedback(self, feedback_text):
        if not os.path.exists(self.feedback_dir):
            try:
                os.makedirs(self.feedback_dir)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"无法创建反馈目录 {self.feedback_dir}: {str(e)}")
                return

        try:
            # 使用时间戳命名反馈文件，确保唯一性
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            feedback_file_path = os.path.join(self.feedback_dir, f"feedback_{timestamp}.txt")
            
            with codecs.open(feedback_file_path, 'w', encoding='utf-8') as f:
                f.write(feedback_text)
            QMessageBox.information(self, "成功", f"反馈已保存至 {feedback_file_path}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存反馈失败: {str(e)}")

class SwitcherWidget(QWidget):
    """切换按钮组件"""
    common_selected = pyqtSignal()
    ai_selected = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)  # 增大边距
        layout.setSpacing(0)
        
        self.button_group = QButtonGroup(self)
        self.button_group.setExclusive(True)

        self.common_button = QPushButton("常见故障查询（FAQ）")
        self.common_button.setCheckable(True)
        self.common_button.setChecked(True)
        self.common_button.setMinimumHeight(60)  # 进一步增大高度
        self.common_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.common_button.toggled.connect(self._on_common_toggled)
        
        self.ai_button = QPushButton("AI故障诊断")
        self.ai_button.setCheckable(True)
        self.ai_button.setMinimumHeight(60)  # 进一步增大高度
        self.ai_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.ai_button.toggled.connect(self._on_ai_toggled)

        self.button_group.addButton(self.common_button)
        self.button_group.addButton(self.ai_button)

        separator = QLabel("|")
        separator.setAlignment(Qt.AlignCenter)
        separator.setFixedWidth(20)  # 增大分隔符宽度
        separator.setStyleSheet(f"""
            font-size: 20px;
            color: #dcdcdc;
        """)  # 增大字体
        
        layout.addWidget(self.common_button)
        layout.addWidget(separator)
        layout.addWidget(self.ai_button)
        
        self.update_button_styles()

    def _on_common_toggled(self, checked):
        if checked:
            self.common_selected.emit()
            self.update_button_styles()
    
    def _on_ai_toggled(self, checked):
        if checked:
            self.ai_selected.emit()
            self.update_button_styles()

    def update_button_styles(self):
        common_style = f"""
            QPushButton {{
                background-color: {"#e3f2fd" if self.common_button.isChecked() else "#f5f5f5"};
                color: {"#1C64F2" if self.common_button.isChecked() else "#333333"};
                border: 1px solid #dcdcdc;
                border-right: none;
                border-radius: 8px 0 0 8px;  /* 增大左侧圆角 */
                padding: 18px;  /* 增大内边距 */
                font-size: 18px;  /* 增大字体 */
                font-family: Microsoft YaHei;
                font-weight: {"bold" if self.common_button.isChecked() else "normal"};
            }}
        """
        
        ai_style = f"""
            QPushButton {{
                background-color: {"#e3f2fd" if self.ai_button.isChecked() else "#f5f5f5"};
                color: {"#1C64F2" if self.ai_button.isChecked() else "#333333"};
                border: 1px solid #dcdcdc;
                border-left: none;
                border-radius: 0 8px 8px 0;  /* 增大右侧圆角 */
                padding: 18px;  /* 增大内边距 */
                font-size: 18px;  /* 增大字体 */
                font-family: Microsoft YaHei;
                font-weight: {"bold" if self.ai_button.isChecked() else "normal"};
            }}
        """
        
        self.common_button.setStyleSheet(common_style)
        self.ai_button.setStyleSheet(ai_style)

class MainApplicationWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0,0,0,0)
        main_layout.setSpacing(0)

        self.switcher = SwitcherWidget()
        main_layout.addWidget(self.switcher)  # 添加切换组件到布局顶部

        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)

        self.common_fault_widget = CommonFaultQueryWidget()
        self.ai_chat_widget = AIChatWidget()

        self.stacked_widget.addWidget(self.common_fault_widget)
        self.stacked_widget.addWidget(self.ai_chat_widget)
        
        # 连接信号到自定义方法，处理切换逻辑
        self.switcher.common_selected.connect(self.on_common_selected)
        self.switcher.ai_selected.connect(self.on_ai_selected)
        
        self.stacked_widget.setCurrentIndex(0)
        
    def on_common_selected(self):
        """处理切换到常见故障查询的逻辑"""
        self.stacked_widget.setCurrentIndex(0)
        # 防止重复加载导致闪烁
        if not hasattr(self, '_common_loaded'):
            try:
                self.common_fault_widget.load_fault_categories()
                self._common_loaded = True
            except Exception as e:
                # 静默处理异常，避免弹出终端窗口
                print(f"加载常见故障查询数据失败: {str(e)}")
                try:
                    self.common_fault_widget = CommonFaultQueryWidget()
                    self.stacked_widget.removeWidget(self.stacked_widget.widget(0))
                    self.stacked_widget.insertWidget(0, self.common_fault_widget)
                    self.stacked_widget.setCurrentIndex(0)
                    self._common_loaded = True
                except Exception as ex:
                    # 静默处理异常
                    print(f"重新创建常见故障查询组件失败: {str(ex)}")
                    pass
        
    def on_ai_selected(self):
        """处理切换到AI故障诊断的逻辑"""
        self.stacked_widget.setCurrentIndex(1)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    font = QFont("Segoe UI", 12)  # 增大默认字体大小
    app.setFont(font)
    
    window = MainApplicationWindow()
    window.show()
    sys.exit(app.exec_()) 

